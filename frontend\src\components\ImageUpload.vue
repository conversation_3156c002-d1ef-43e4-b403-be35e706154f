<!--
  图片上传组件
  支持拖拽上传和点击上传
-->

<template>
  <div class="image-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      accept="image/*"
      drag
    >
      <div v-if="!imageUrl" class="upload-area">
        <el-icon class="upload-icon"><Plus /></el-icon>
        <div class="upload-text">点击或拖拽图片到此处上传</div>
        <div class="upload-hint">支持 JPG、PNG、GIF 格式，大小不超过 2MB</div>
      </div>
      <div v-else class="image-preview">
        <img :src="imageUrl" alt="预览图片" />
        <div class="image-overlay">
          <el-icon class="overlay-icon" @click.stop="removeImage"
            ><Delete
          /></el-icon>
        </div>
      </div>
    </el-upload>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/user";

const userStore = useUserStore();
const uploadRef = ref();

// 上传配置
const uploadUrl = computed(() => "http://localhost:8001/api/upload");
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`,
}));

// 图片URL
const imageUrl = computed({
  get: () => {
    if (!props.modelValue) return "";
    // 如果已经是完整URL，直接返回；否则拼接服务器地址
    if (props.modelValue.startsWith("http")) {
      return props.modelValue;
    }
    return `http://localhost:8001${props.modelValue}`;
  },
  set: (value) => emit("update:modelValue", value),
});

// 上传前检查
const beforeUpload = (file) => {
  const isImage = file.type.startsWith("image/");
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return false;
  }
  if (!isLt2M) {
    ElMessage.error("图片大小不能超过 2MB!");
    return false;
  }
  return true;
};

// 上传成功
const handleSuccess = (response) => {
  if (response.url) {
    emit("update:modelValue", response.url);
    ElMessage.success("图片上传成功");
  }
};

// 上传失败
const handleError = (error) => {
  console.error("上传失败:", error);
  ElMessage.error("图片上传失败");
};

// 移除图片
const removeImage = () => {
  emit("update:modelValue", "");
};

return {
  uploadRef,
  uploadUrl,
  uploadHeaders,
  imageUrl,
  beforeUpload,
  handleSuccess,
  handleError,
  removeImage,
};
</script>

<style scoped>
.image-upload {
  width: 100%;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
}

.image-preview {
  position: relative;
  width: 100%;
  max-width: 200px;
  margin: 0 auto;
}

.image-preview img {
  width: 100%;
  height: auto;
  border-radius: 6px;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 6px;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.overlay-icon {
  color: white;
  font-size: 20px;
  cursor: pointer;
}

/* Element Plus上传组件样式覆盖 */
:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: auto;
  border: none;
  background: transparent;
}
</style>
