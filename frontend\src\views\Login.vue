<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>留言墙登录</h2>
        <p>欢迎回来，请登录您的账号</p>
      </div>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginFormRules"
        class="login-form"
        @submit.prevent="handleLogin"
        ><el-form-item
          ><el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
          ></el-input
        ></el-form-item>
        <el-form-item
          ><el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          ></el-input
        ></el-form-item>
        <el-form-item
          ><el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-btn"
            >登录</el-button
          ></el-form-item
        >
      </el-form>
      <div class="login-footer">
        <span>还没有账号?</span>
        <router-link to="/register" class="register-link">立即注册</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/user";
import { useRouter } from "vue-router";

const loginFormRef = ref();
const loading = ref(false);
const userStore = useUserStore();
const router = useRouter();

// 登录表单的数据
const loginForm = reactive({
  username: "",
  password: "",
});
// 表单验证规则
const loginFormRules = {
  username: [
    { requried: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 20,
      message: "用户名长度在3到20个字符之间",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在6到20个字符之间", trigger: "blur" },
  ],
};

// 处理登录
const handleLogin = async () => {
  // 如果表单验证不通过，则直接返回
  if (!loginFormRef.value) return;
  // 如果表单验证通过，则调用登录接口
  try {
    const valid = await loginFormRef.value.validate();
    if (!valid) return;
    loading.value = true;
    const result = await userStore.login(
      loginForm.username,
      loginForm.password
    );
    if (result.success) {
      ElMessage.success("登录成功");
      router.push("/home");
    } else {
      ElMessage.error(result.message);
    }
  } catch (error) {
    console.error("登录错误", error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.login-form {
  margin-bottom: 20px;
}

.login-btn {
  width: 100%;
}

.login-footer {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.register-link {
  color: #409eff;
  text-decoration: none;
  margin-left: 5px;
}

.register-link:hover {
  text-decoration: underline;
}
</style>
