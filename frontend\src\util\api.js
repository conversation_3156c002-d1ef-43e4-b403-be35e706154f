import axios from "axios";
import { ElMessage } from "element-plus";

// 创建axios实例
const api = axios.create({
  // 请求后端的前缀地址
  baseURL: "http://localhost:8001/",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 在请求发送之前做些什么
    // 添加认证token
    config.headers.Authorization = "Bearer " + localStorage.getItem("token");
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    // 把错误丢出去可以catch到
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 统一处理错误
    if (error.response) {
      const { status, data } = error.response;
      switch (status) {
        case 401:
          // 检测是否是用户被禁用的错误
          if (data?.detail == "该账号已被禁用") {
            ElMessage.error("该账号已被封禁,请联系管理员");
            // 清除本地存储并跳转到登录页
            // 清除本地存储的token字段
            localStorage.removeItem("token");
            localStorage.removeItem("userInfo");
            // 删除请求头中的认证字段，一般就是存放token
            delete api.defaults.headers.common["Authorization"];
            // 如果当前目录不是登录页则直接跳转到登录页
            if (window.location.pathname != "/login") {
              window.location.href = "/login";
            }
          } else {
            ElMessage.error("登录已过期，请重新登录");
            // 清除本地存储
            localStorage.removeItem("token");
            localStorage.removeItem("userInfo");
            delete api.defaults.headers.common["Authorization"];
            if (window.location.pathname !== "/login") {
              window.location.href = "/login";
            }
          }
          break;
        case 403:
          ElMessage.error("权限不足");
          break;
        case 404:
          ElMessage.error("请求资源不存在");
          break;
        case 500:
          ElMessage.error("服务器内部错误");
          break;
        default:
          ElMessage.error(data?.detail || "请求失败");
      }
    } else if (error.request) {
      ElMessage.error("请求超时");
    } else {
      ElMessage.error("请求配置错误");
    }
    return Promise.reject(error);
  }
);

export default api;
