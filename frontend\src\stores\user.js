import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import api from "@/util/api";

// 导出存储的用户信息
export const useUserStore = defineStore("user", () => {
  // 状态
  const token = ref(localStorage.getItem("token") || "");
  const userInfo = ref(JSON.parse(localStorage.getItem("userInfo") || "null"));

  // 计算属性 是否已经登录有token值
  const isLoggedIn = computed(() => !!token.value);
  // 登录状态
  const login = async (username, password) => {
    try {
      const response = await api.post("/api/login", { username, password });
      const { access_token, user } = response.data;
      // 检查用户状态信息
      if (!user.is_active) {
        return {
          success: false,
          message: "该用户已被封禁，请联系管理员",
        };
      }
      // 保存token和用户信息
      token.value = access_token;
      userInfo.value = user;

      // 持久化存储
      localStorage.setItem("token", access_token);
      localStorage.setItem("userInfo", JSON.stringify(user));

      // 设置API默认请求头
      api.defaults.headers.common["Authorization"] = `Bearer ${access_token}`;

      return { success: true };
    } catch (error) {
      console.error("登录失败：", error);
      return {
        success: false,
        message: error.response?.detail || "登录失败",
      };
    }
  };
  // 注册
  const register = async (username, password, nickname) => {
    try {
      await api.post("/api/register", {
        username,
        password,
        nickname,
      });
      return { success: true };
    } catch (error) {
      console.error("注册失败：", error);
      return {
        success: false,
        message: error.response?.data?.detail || "注册失败",
      };
    }
  };
  // 登出
  const logout = () => {
    token.value = "";
    userInfo.value = null;

    // 清除本地存储
    localStorage.removeItem("token");
    localStorage.removeItem("userInfo");

    // 清除API请求头
    delete api.defaults.headers.common["Authorization"];
  };
  // 更新用户信息
  const updateProfile = async (nickname, avatar) => {
    try {
      const response = await api.put("/api/user/profile", {
        nickname,
        avatar,
      });
      // 更新本地用户信息
      userInfo.value = response.data;
      localStorage.setItem("userInfo", JSON.stringify(response.data));
      return { success: true };
    } catch (error) {
      console.error("更新用户信息失败:", error);
      return {
        success: false,
        message: error.response?.data?.detail || "更新失败",
      };
    }
  };

  // 检查用户状态
  const checkUserStatus = async () => {
    if (!isLoggedIn.value) return;
    try {
      const response = await api.get("/api/user/profile");
      const userData = response.data;

      // 检查用户是否已经被禁用
      if (!userData.is_active) {
        logout();
        ElMessage.error("该账号已被封禁，请联系管理员");
        return false;
      }

      // 更新用户信息
      userInfo.value = userData;
      localStorage.setItem("userInfo", JSON.stringify(userData));
      return true;
    } catch (error) {
      // 如果请求失败，则可能是token过期或账号被禁用
      console.error("检查用户状态失败:", error);
      return false;
    }
  };
  // 初始化时设置API请求头
  if (token.value) {
    api.defaults.headers.common["Authorization"] = `Bearer ${token.value}`;
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    login,
    register,
    logout,
    updateProfile,
    checkUserStatus,
  };
});
