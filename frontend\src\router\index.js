/**
 * Vue Router路由配置
 **/
import { createRouter, createWebHistory } from "vue-router";
import { ElMessage } from "element-plus";

// 路由配置
const routes = [
  // 根目录重新跳转到login页面
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/Login.vue"),
    // meta: {
    //   // 需要未登录的用户才可访问
    //   requiresGuest: true,
    // },
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("@/views/Register.vue"),
    // meta: {
    //   // 需要未登录的用户才可访问
    //   requiresGuest: true,
    // },
  },
  {
    path: "/home",
    name: "Home",
    component: () => import("@/views/Home.vue"),
    // meta: {
    //   // 需要未登录的用户才可访问
    //   requiresGuest: true,
    // },
    children: [
      { path: "", redirect: "/home/<USER>" },
      {
        path: "messages",
        name: "Messages",
        component: () => import("@/views/Messages.vue"),
      },
    ],
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫 未启用！
// router.before;

export default router;
