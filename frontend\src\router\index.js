/**
 * Vue Router路由配置
 **/
import { createRouter, createWebHistory } from "vue-router";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/user";

// 路由配置
const routes = [
  // 根目录重新跳转到login页面
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/Login.vue"),
    meta: {
      requiresGuest: true, // 需要未登录的用户才可访问
    },
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("@/views/Register.vue"),
    meta: {
      requiresGuest: true, // 需要未登录的用户才可访问
    },
  },
  {
    path: "/home",
    name: "Home",
    component: () => import("@/views/Home.vue"),
    meta: {
      requiresAuth: true, // 需要登录才能访问
    },
    children: [
      { path: "", redirect: "/home/<USER>" },
      {
        path: "messages",
        name: "Messages",
        component: () => import("@/views/Messages.vue"),
        meta: {
          requiresAuth: true,
        },
      },
      {
        path: "profile",
        name: "Profile",
        component: () => import("@/views/Profile.vue"),
        meta: {
          requiresAuth: true,
        },
      },
    ],
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();
  const isLoggedIn = userStore.isLoggedIn;
  const userInfo = userStore.userInfo;

  // 检查是否需要登录
  if (to.meta.requiresAuth && !isLoggedIn) {
    ElMessage.warning("请先登录");
    next("/login");
    return;
  }

  // 检查是否需要游客状态（未登录）
  if (to.meta.requiresGuest && isLoggedIn) {
    next("/home/<USER>");
    return;
  }

  // 检查用户是否被禁用
  if (isLoggedIn && userInfo && !userInfo.is_active) {
    ElMessage.error("账号已被禁用，请联系管理员");
    userStore.logout();
    next("/login");
    return;
  }

  next();
});

export default router;
