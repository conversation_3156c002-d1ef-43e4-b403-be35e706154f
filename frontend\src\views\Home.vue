<template>
  <div class="home-container">
    <!-- 顶部导航 -->
    <el-header class="header">
      <div class="header-content">
        <div class="logo">
          <h2>留言墙</h2>
        </div>
        <div class="nav-menu">
          <el-menu
            :default-active="activeMenu"
            mode="horizontal"
            @select="handleMenuSelect"
            class="nav-menu-items"
            ><el-menu-item index="/home/<USER>">
              <el-icon><ChatDotRound /></el-icon>
              <span>留言墙</span>
            </el-menu-item>
            <el-menu-item index="/home/<USER>">
              <el-icon><User /></el-icon>
              <span>个人信息</span>
            </el-menu-item>
            <el-menu-item
              v-if="userStore.userInfo?.role === 'admin'"
              index="/home/<USER>"
            >
              <el-icon><Setting /></el-icon>
              <span>管理后台</span>
            </el-menu-item>
          </el-menu>
        </div>

        <div class="user-info">
          <el-dropdown @command="handleUserCommand">
            <span class="user-dropdown">
              <el-avatar
                :src="getAvatarUrl(userStore.userInfo?.avatar)"
                :size="32"
              >
                {{ userStore.userInfo?.nickname?.charAt(0) || "U" }}
              </el-avatar>
              <span class="username">{{ userStore.userInfo?.nickname }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>
    <!-- 主内容区域 -->
    <el-main class="main-content">
      <router-view />
    </el-main>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { useUserStore } from "@/stores/user";
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 当前激活的菜单项
const activeMenu = computed(() => route.path);

// 获取头像URL
const getAvatarUrl = (avatar) => {
  // 若头像不存在则返回空
  if (!avatar) return "";
  // 若头像为本地图片则直接返回本地路径
  if (avatar.startsWith("http")) return avatar;
  return `http://localhost:8001${avatar}`;
};
// 处理菜单选择
const handleMenuSelect = (index) => {
  // 路由跳转
  router.push(index);
};

// 处理用户下拉菜单命令
const handleUserCommand = async (command) => {
  if (command === "logout") {
    try {
      await ElMessageBox.confirm("确定要退出登录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
      userStore.logout();
      ElMessage.success("已退出登录");
      router.push("/login");
    } catch {
      //用户取消操作}
    }
  }
};
// 定期检查用户状态
let statusCheckInterval = null;
onMounted(() => {
  // 每5分钟检查一次用户状态
  statusCheckInterval = setInterval(async () => {
    const isValid = await userStore.checkUserStatus();
    if (!isValid) {
      // 若用户状态无效，则跳转登录页
      router.push("/login");
    }
  }, 5 * 60 * 1000); //5分钟
});
onUnmounted(() => {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval);
  }
});
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  height: 60px;
  line-height: 60px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo h2 {
  color: #409eff;
  margin: 0;
  font-size: 20px;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-menu-items {
  border-bottom: none;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  color: #333;
  font-size: 14px;
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: #f5f5f5;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Element Plus菜单样式覆盖 */
:deep(.el-menu--horizontal) {
  border-bottom: none;
}

:deep(.el-menu-item) {
  border-bottom: 2px solid transparent;
}

:deep(.el-menu-item.is-active) {
  border-bottom-color: #409eff;
  color: #409eff;
}
</style>
