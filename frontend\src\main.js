import { createApp } from "vue";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import App from "./App.vue";
import router from "./router";
// 创建Vue应用实例
const app = createApp(App);

// 注册Element Plus图标 Object.entries() 将对象转换为数组方便for of遍历[key,value]
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  // 每个图标组件都以键值对的形式注册到组件中可以通过 <icon/>
  app.component(key, component);
}

// 插件注册
app.use(router); // 路由注册
app.use(createPinia()); // pinia注册
app.use(ElementPlus);

// 挂载
app.mount("#app");
