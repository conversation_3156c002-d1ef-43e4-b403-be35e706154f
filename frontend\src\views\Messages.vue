<template>
  <div class="messages-container">
    <el-card class="publish-card" shadow="hover">
      <template #header>
        <div class="card-header"><span>发布留言</span></div>
      </template>
      <el-form @submit.prevent="handlePublish">
        <el-form-item>
          <el-input
            v-model="newMessage"
            type="textarea"
            :rows="4"
            placeholder="分享你的想法..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="publishing"
            :disabled="!newMessage.trim()"
            @click="handlePublish"
            >发布留言</el-button
          >
        </el-form-item>
      </el-form>
    </el-card>
    <!-- 留言列表 -->
    <div class="messages-list">
      <el-card
        v-for="message in messages"
        :key="message.id"
        class="message-card"
        shadow="hover"
        ><div class="message-content">
          <div class="message-header">
            <div class="author-info">
              <el-avatar :src="getAvatarUrl(message.author.avatar)" :size="40">
                {{ message.author.nickname.charAt(0) }}
              </el-avatar>
              <div class="author-details">
                <div class="author-name">{{ message.author.nickname }}</div>
                <div class="message-time">
                  {{ formatTime(message.created_at) }}
                </div>
              </div>
            </div>
          </div>
          <div class="message-text">{{ message.content }}</div>
        </div>
        <!-- 操作按钮 -->
        <div class="message-actions">
          <el-button
            :type="message.is_liked ? 'primary' : 'default'"
            :icon="message.is_liked ? 'StarFilled' : 'Star'"
            size="small"
            @click="toggleLike(message)"
            >{{ message.likes_count || 0 }}</el-button
          >
          <el-button
            type="default"
            icon="ChatDotRound"
            size="small"
            @click="toggleComments(message)"
            >{{ message.comments_count || 0 }}</el-button
          >
        </div>
        <!-- 评论区域 -->
        <div v-if="message.showComments" class="comments-section">
          <!-- 评论输入 -->
          <div class="comment-input">
            <el-input
              v-model="message.newComment"
              type="textarea"
              :rows="2"
              placeholder="写下你的评论..."
              maxlength="200"
              show-word-limit
            />
            <el-button
              type="primary"
              size="small"
              :loading="message.commenting"
              :disabled="!message.newComment?.trim()"
              @click="submitComment(message)"
              style="margin-top: 8px"
              >发表评论</el-button
            >
          </div>
        </div>
        <!-- 评论列表 -->
        <div v-if="message.comments?.length" class="comments-list">
          <div
            v-for="comment in message.comments"
            :key="comment.id"
            class="comment-item"
          >
            <div class="comment-header">
              <el-avatar :src="getAvatarUrl(comment.author.avatar)" :size="24">
                {{ comment.author.nickname.charAt(0) }}
              </el-avatar>
              <span class="comment-author">{{ comment.author.nickname }}</span>
              <span class="comment-time">{{
                formatTime(comment.created_at)
              }}</span>
            </div>
            <div class="comment-content">{{ comment.content }}</div>
          </div>
        </div>
      </el-card>
      <!-- 分页操作 -->
      <div v-if="hasMore" class="load-more">
        <el-button :loading="loading" @click="handleLoadMore" type="text"
          >加载更多</el-button
        >
      </div>
      <!-- 空状态 -->
      <el-empty
        v-if="!loading && messages.length === 0"
        description="暂无留言,快来发布一条吧"
      ></el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import api from "@/util/api";
const messages = ref([]);
const newMessage = ref("");
const loading = ref(false);
const publishing = ref(false);
const hasMore = ref(true);
const currentPage = ref(0);
const pageSize = 5;

// 获取头像URL
const getAvatarUrl = (avatar) => {
  if (!avatar) return "";
  if (avatar.startsWith("http")) return avatar;
  return `http://localhost:8001${avatar}`;
};

// 格式化时间
const formatTime = (timeString) => {
  // 解析时间字符串，后端返回的是北京时间
  const time = new Date(timeString);
  const now = new Date();

  // 计算时间差
  const diff = now - time;

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return "刚刚";
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 30) return `${days}天前`;

  // 格式化为北京时间显示
  return time.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};
// 加载留言列表
const loadMessages = async (reset = false) => {
  if (loading.value) return;

  try {
    loading.value = true;

    const skip = reset ? 0 : messages.value.length;
    console.log(
      `加载留言 - reset: ${reset}, skip: ${skip}, limit: ${pageSize}`
    );

    const response = await api.get("/api/messages", {
      params: { skip, limit: pageSize },
    });

    console.log(`收到 ${response.data.length} 条留言`);

    const newMessages = response.data.map((message) => ({
      ...message,
      showComments: false,
      newComment: "",
      commenting: false,
      comments: [],
    }));

    if (reset) {
      messages.value = newMessages;
      currentPage.value = 1;
      console.log("重置留言列表");
    } else {
      messages.value.push(...newMessages);
      currentPage.value++;
      console.log(`追加留言，当前总数: ${messages.value.length}`);
    }

    // 判断是否还有更多数据
    hasMore.value = newMessages.length === pageSize;
    console.log(`是否还有更多: ${hasMore.value}`);
  } catch (error) {
    console.error("加载留言失败:", error);
    ElMessage.error("加载留言失败");
  } finally {
    loading.value = false;
  }
};
// 发布留言
const handlePublish = async () => {
  if (!newMessage.value.trim()) {
    ElMessage.warning("请输入留言内容");
    return;
  }

  try {
    publishing.value = true;

    const response = await api.post("/api/messages", {
      content: newMessage.value.trim(),
    });

    // 将新留言添加到列表顶部
    messages.value.unshift(response.data);
    newMessage.value = "";

    ElMessage.success("留言发布成功");
  } catch (error) {
    console.error("发布留言失败:", error);
    ElMessage.error("发布留言失败");
  } finally {
    publishing.value = false;
  }
};
// 切换点赞
const toggleLike = async (message) => {
  try {
    const response = await api.post(`/api/messages/${message.id}/like`);

    // 更新本地状态
    message.is_liked = response.data.liked;
    if (response.data.liked) {
      message.likes_count = (message.likes_count || 0) + 1;
    } else {
      message.likes_count = Math.max((message.likes_count || 0) - 1, 0);
    }

    ElMessage.success(response.data.message);
  } catch (error) {
    console.error("点赞操作失败:", error);
    ElMessage.error("点赞操作失败");
  }
};

// 切换评论显示
const toggleComments = async (message) => {
  message.showComments = !message.showComments;

  // 如果是首次打开评论区，加载评论
  if (message.showComments && !message.comments.length) {
    await loadComments(message);
  }
};

// 加载评论
const loadComments = async (message) => {
  try {
    const response = await api.get(`/api/messages/${message.id}/comments`);
    message.comments = response.data;
  } catch (error) {
    console.error("加载评论失败:", error);
    ElMessage.error("加载评论失败");
  }
};

// 提交评论
const submitComment = async (message) => {
  if (!message.newComment?.trim()) {
    ElMessage.warning("请输入评论内容");
    return;
  }

  try {
    message.commenting = true;

    const response = await api.post("/api/comments", {
      content: message.newComment.trim(),
      message_id: message.id,
    });

    // 添加新评论到列表
    message.comments.push(response.data);
    message.comments_count = (message.comments_count || 0) + 1;
    message.newComment = "";

    ElMessage.success("评论发表成功");
  } catch (error) {
    console.error("发表评论失败:", error);
    ElMessage.error("发表评论失败");
  } finally {
    message.commenting = false;
  }
};

// 组件挂载时加载留言
onMounted(() => {
  loadMessages(true);
});
</script>

<style coped>
.messages-container {
  max-width: 800px;
  margin: 0 auto;
}

.publish-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: 600;
  color: #333;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message-card {
  transition: transform 0.2s;
}

.message-card:hover {
  transform: translateY(-2px);
}

.message-content {
  padding: 10px 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.author-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.message-time {
  color: #999;
  font-size: 12px;
}

.message-text {
  color: #333;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;
}

.load-more {
  text-align: center;
  padding: 20px;
}

/* 操作按钮 */
.message-actions {
  display: flex;
  gap: 12px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

/* 评论区域 */
.comments-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.comment-input {
  margin-bottom: 15px;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.comment-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 600;
  color: #333;
  font-size: 13px;
}

.comment-time {
  color: #999;
  font-size: 12px;
  margin-left: auto;
}

.comment-content {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .messages-container {
    padding: 0 10px;
  }

  .message-card {
    margin: 0 -10px;
  }

  .message-actions {
    flex-wrap: wrap;
  }
}
</style>
