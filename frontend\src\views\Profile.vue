<template>
  <div class="profile-container">
    <el-card class="profile-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>个人信息</span>
        </div>
      </template>

      <div class="profile-content">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <div class="avatar-wrapper">
            <el-avatar
              :src="getAvatarUrl(userStore.userInfo?.avatar)"
              :size="100"
            >
              {{ userStore.userInfo?.nickname?.charAt(0) || "U" }}
            </el-avatar>
            <div class="avatar-upload">
              <el-button size="small" type="text" @click="handleAvatarClick">
                更换头像
              </el-button>
            </div>
          </div>
        </div>

        <!-- 信息表单 -->
        <el-form
          ref="profileFormRef"
          :model="profileForm"
          :rules="profileRules"
          label-width="80px"
          class="profile-form"
        >
          <el-form-item label="用户名">
            <el-input
              :value="userStore.userInfo?.username"
              disabled
              placeholder="用户名不可修改"
            />
          </el-form-item>

          <el-form-item label="昵称" prop="nickname">
            <el-input
              v-model="profileForm.nickname"
              placeholder="请输入昵称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="头像" prop="avatar">
            <ImageUpload v-model="profileForm.avatar" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" :loading="saving" @click="handleSave">
              保存修改
            </el-button>
            <el-button @click="handleReset"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 头像预览对话框 -->
    <el-dialog v-model="previewVisible" title="头像预览" width="300px" center>
      <div class="avatar-preview">
        <el-avatar :src="profileForm.avatar" :size="150">
          {{ profileForm.nickname?.charAt(0) || "U" }}
        </el-avatar>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useUserStore } from "@/stores/user";
import ImageUpload from "@/components/ImageUpload.vue";
const userStore = useUserStore();
const profileFormRef = ref();
const saving = ref(false);
const previewVisible = ref(false);

// 个人信息表单
const profileForm = reactive({
  nickname: "",
  avatar: "",
});

// 表单验证规则
const profileRules = {
  nickname: [
    { required: true, message: "请输入昵称", trigger: "blur" },
    { min: 1, max: 50, message: "昵称长度在1到50个字符", trigger: "blur" },
  ],
};

// 获取头像URL
const getAvatarUrl = (avatar) => {
  if (!avatar) return "";
  if (avatar.startsWith("http")) return avatar;
  return `http://localhost:8001${avatar}`;
};

// 初始化表单数据
const initForm = () => {
  if (userStore.userInfo) {
    profileForm.nickname = userStore.userInfo.nickname;
    profileForm.avatar = userStore.userInfo.avatar || "";
  }
};

// 处理头像点击
const handleAvatarClick = () => {
  ElMessageBox.prompt("请输入头像图片链接", "更换头像", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputPattern: /^https?:\/\/.+/,
    inputErrorMessage: "请输入有效的图片链接",
  })
    .then(({ value }) => {
      profileForm.avatar = value;
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 预览头像
const previewAvatar = () => {
  if (profileForm.avatar) {
    previewVisible.value = true;
  }
};

// 保存修改
const handleSave = async () => {
  if (!profileFormRef.value) return;

  try {
    const valid = await profileFormRef.value.validate();
    if (!valid) return;

    saving.value = true;

    const result = await userStore.updateProfile(
      profileForm.nickname,
      profileForm.avatar
    );

    if (result.success) {
      ElMessage.success("个人信息更新成功");
      // 重新初始化表单数据以反映最新的用户信息
      initForm();
    } else {
      ElMessage.error(result.message);
    }
  } catch (error) {
    console.error("更新个人信息失败:", error);
  } finally {
    saving.value = false;
  }
};

// 重置表单
const handleReset = () => {
  initForm();
  ElMessage.info("已重置为原始信息");
};

// 组件挂载时初始化
onMounted(() => {
  initForm();
});
</script>

<style scoped>
.profile-container {
  max-width: 600px;
  margin: 0 auto;
}

.profile-card {
  min-height: 500px;
}

.card-header {
  font-weight: 600;
  color: #333;
}

.profile-content {
  padding: 20px 0;
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.avatar-upload {
  text-align: center;
}

.profile-form {
  max-width: 400px;
  margin: 0 auto;
}

.avatar-preview {
  display: flex;
  justify-content: center;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 0 10px;
  }

  .profile-form {
    max-width: 100%;
  }
}
</style>
